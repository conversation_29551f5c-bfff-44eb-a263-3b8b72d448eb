# Sync State Table Integration Plan

## Overview
This plan outlines how to integrate an improved `sync_state` table into the existing sync system to dramatically improve startup performance by enabling fast change detection through hash comparison instead of full database scans.

## Current Sync System Architecture

### Key Components
1. **unified-sync-engine.ts** - Main sync orchestrator
2. **change-detector.ts** - Detects changes between local DB and manifest
3. **manifest-manager.ts** - Manages sync-manifest.json files
4. **database-hooks.ts** - Detects database changes for auto-sync
5. **auto-sync.ts** - Handles automatic sync triggers
6. **sync-api.ts** - Public API for sync operations

### Current Flow
```
App Startup → Load Manifest → Compare with DB → Detect Changes → Sync
                                    ↑
                            (Slow: Full DB scan)
```

### Target Flow with sync_state
```
App Startup → Load Manifest → Compare with sync_state → Fast Change Detection → Sync
                                         ↑
                                (Fast: Hash comparison)
```

## Phase 1: Database Schema Updates

### 1.1 Update sync_state Table
**File**: `electron/main/database/database.ts`

Add migration to update the existing sync_state table:

```sql
-- Add new columns to existing sync_state table
ALTER TABLE sync_state ADD COLUMN composite_id TEXT;
ALTER TABLE sync_state ADD COLUMN item_name TEXT;
ALTER TABLE sync_state ADD COLUMN sync_path TEXT;
ALTER TABLE sync_state ADD COLUMN last_modified TEXT;

-- Create computed column for composite_id
UPDATE sync_state SET composite_id = item_type || '_' || item_id WHERE composite_id IS NULL;

-- Add new indexes for performance
CREATE INDEX IF NOT EXISTS idx_sync_state_composite_id ON sync_state(composite_id);
CREATE INDEX IF NOT EXISTS idx_sync_state_hash ON sync_state(sync_hash);
CREATE INDEX IF NOT EXISTS idx_sync_state_modified ON sync_state(last_modified);
```

### 1.2 Update TypeScript Interface
**File**: `electron/main/api/sync-logic/types.ts`

Fix the SyncState interface to match actual table:

```typescript
export interface SyncState {
  item_type: 'book' | 'folder' | 'note';
  item_id: number;
  composite_id: string;
  sync_hash: string;
  item_name: string;
  sync_path: string;
  last_modified: string;
  last_synced: string;
  device_id: string;
  sync_version: number;
}
```

## Phase 2: Database Hooks Integration

### 2.1 Enhance Database Hooks
**File**: `electron/main/database/database-hooks.ts`

Add sync_state population to database hooks:

```typescript
// Add new method to DatabaseHooksManager
private async updateSyncState(
  type: DatabaseChangeType,
  itemType: DatabaseItemType,
  itemId: number,
  details?: any
): Promise<void> {
  if (type === 'delete') {
    // Remove from sync_state
    await dbRun('DELETE FROM sync_state WHERE item_type = ? AND item_id = ?', 
                [itemType, itemId]);
    return;
  }

  // Get current item data
  const item = await this.getCurrentItemData(itemType, itemId);
  if (!item) return;

  // Calculate hash and path
  const hash = this.calculateItemHash(item);
  const name = item.title || item.name || 'Untitled';
  const path = await this.generateItemPath(item);
  const modified = item.updated_at || item.created_at || new Date().toISOString();
  const deviceId = await this.getDeviceId();

  // Update sync_state
  await dbRun(`
    INSERT OR REPLACE INTO sync_state 
    (item_type, item_id, composite_id, sync_hash, item_name, sync_path, 
     last_modified, last_synced, device_id, sync_version)
    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, 1)
  `, [itemType, itemId, `${itemType}_${itemId}`, hash, name, path, modified, deviceId]);
}
```

### 2.2 Integrate with Existing Change Notifications
Update the `notifyChange` method to also update sync_state:

```typescript
public notifyChange(
  type: DatabaseChangeType,
  itemType: DatabaseItemType,
  itemId: number,
  details?: any,
  options: DatabaseHookOptions = {}
): void {
  // Existing logic...
  
  // Update sync_state table
  if (!options.skipSyncStateUpdate) {
    this.updateSyncState(type, itemType, itemId, details).catch(error => {
      console.error('[DatabaseHooks] Failed to update sync_state:', error);
    });
  }
  
  // Rest of existing logic...
}
```

## Phase 3: Fast Change Detection

### 3.1 Create Fast Change Detector
**File**: `electron/main/api/sync-logic/fast-change-detector.ts`

New class for optimized change detection:

```typescript
export class FastChangeDetector {
  /**
   * Fast manifest comparison using sync_state table
   */
  async compareWithSyncState(manifest: SyncManifest): Promise<Changes> {
    // Get all sync state in one query
    const syncState = await dbAll<SyncState>(`
      SELECT composite_id, sync_hash, item_name, sync_path, last_modified, item_type
      FROM sync_state
    `);
    
    const syncMap = new Map(syncState.map(s => [s.composite_id, s]));
    
    const toImport: ManifestItemsByType = { books: [], folders: [], notes: [] };
    const toExport: LocalItemsByType = { books: [], folders: [], notes: [] };
    const conflicts: ConflictItem[] = [];
    
    // Check manifest items against sync_state
    for (const manifestItem of manifest.items) {
      const syncItem = syncMap.get(manifestItem.id);
      
      if (!syncItem) {
        // New item in manifest - import it
        this.categorizeManifestItem(manifestItem, toImport);
      } else if (this.hasConflict(manifestItem, syncItem)) {
        // Hash/name/path mismatch - conflict
        conflicts.push({
          local: this.convertSyncStateToManifestItem(syncItem),
          remote: manifestItem,
          type: 'content'
        });
      }
      
      // Remove processed items
      syncMap.delete(manifestItem.id);
    }
    
    // Remaining items in syncMap are local-only (export them)
    for (const [id, syncItem] of syncMap) {
      this.categorizeSyncStateItem(syncItem, toExport);
    }
    
    return { toImport, toExport, conflicts, toDelete: [] };
  }
  
  private hasConflict(manifestItem: ManifestItem, syncItem: SyncState): boolean {
    return manifestItem.hash !== syncItem.sync_hash ||
           manifestItem.name !== syncItem.item_name ||
           manifestItem.path !== syncItem.sync_path;
  }
}
```

### 3.2 Update Change Detector
**File**: `electron/main/api/sync-logic/change-detector.ts`

Add fast path to existing change detector:

```typescript
export class ChangeDetector {
  private fastDetector = new FastChangeDetector();
  
  async compareStates(manifest: SyncManifest, syncPath: string): Promise<Changes> {
    // Try fast detection first
    try {
      console.log('[ChangeDetector] Using fast sync_state comparison');
      return await this.fastDetector.compareWithSyncState(manifest);
    } catch (error) {
      console.warn('[ChangeDetector] Fast detection failed, falling back to full scan:', error);
      // Fallback to existing slow method
      return await this.compareStatesLegacy(manifest, syncPath);
    }
  }
  
  // Rename existing method
  private async compareStatesLegacy(manifest: SyncManifest, syncPath: string): Promise<Changes> {
    // Move existing compareStates implementation here
    // ... existing logic ...
  }
}
```

## Phase 4: Sync State Maintenance

### 4.1 Sync State Updater
**File**: `electron/main/api/sync-logic/sync-state-updater.ts`

New service to maintain sync_state accuracy:

```typescript
export class SyncStateUpdater {
  /**
   * Update sync_state after successful sync operation
   */
  async updateAfterSync(
    itemType: string, 
    itemId: number, 
    manifestItem: ManifestItem
  ): Promise<void> {
    await dbRun(`
      UPDATE sync_state 
      SET sync_hash = ?, 
          item_name = ?, 
          sync_path = ?, 
          last_modified = ?,
          last_synced = CURRENT_TIMESTAMP
      WHERE item_type = ? AND item_id = ?
    `, [
      manifestItem.hash,
      manifestItem.name,
      manifestItem.path,
      manifestItem.modified,
      itemType,
      itemId
    ]);
  }
  
  /**
   * Rebuild sync_state from current database state
   */
  async rebuildSyncState(): Promise<void> {
    console.log('[SyncStateUpdater] Rebuilding sync_state table...');
    
    // Clear existing data
    await dbRun('DELETE FROM sync_state');
    
    // Rebuild from current database
    const books = await this.getAllBooks();
    const folders = await this.getAllFolders();
    const notes = await this.getAllNotes();
    
    for (const book of books) {
      await this.insertSyncStateItem('book', book);
    }
    
    for (const folder of folders) {
      await this.insertSyncStateItem('folder', folder);
    }
    
    for (const note of notes) {
      await this.insertSyncStateItem('note', note);
    }
    
    console.log('[SyncStateUpdater] Sync state rebuilt successfully');
  }
}
```

### 4.2 Integration with Unified Sync Engine
**File**: `electron/main/api/sync-logic/unified-sync-engine.ts`

Update sync engine to maintain sync_state:

```typescript
export class UnifiedSyncEngine extends EventEmitter {
  private syncStateUpdater = new SyncStateUpdater();

  // Update existing import methods
  private async importBook(manifestItem: ManifestItem): Promise<void> {
    // Existing import logic...
    const book = await createBook(bookData);

    // Update sync_state after successful import
    await this.syncStateUpdater.updateAfterSync('book', book.id, manifestItem);
  }

  private async importFolder(manifestItem: ManifestItem): Promise<void> {
    // Existing import logic...
    const folder = await createFolder(folderData);

    // Update sync_state after successful import
    await this.syncStateUpdater.updateAfterSync('folder', folder.id, manifestItem);
  }

  private async importNote(manifestItem: ManifestItem): Promise<void> {
    // Existing import logic...
    const note = await createNote(noteData);

    // Update sync_state after successful import
    await this.syncStateUpdater.updateAfterSync('note', note.id, manifestItem);
  }
}
```

## Phase 5: Performance Optimizations

### 5.1 Batch Operations
Optimize database operations for better performance:

```typescript
// In sync-state-updater.ts
async batchUpdateSyncState(updates: SyncStateUpdate[]): Promise<void> {
  await withTransaction(async () => {
    for (const update of updates) {
      await this.updateAfterSync(update.itemType, update.itemId, update.manifestItem);
    }
  });
}
```

### 5.2 Startup Optimization
**File**: `electron/main/api/sync-logic/sync-api.ts`

Add startup sync state validation:

```typescript
export class SyncAPI extends EventEmitter {
  async initializeSync(): Promise<void> {
    // Check if sync_state is populated
    const syncStateCount = await dbGet<{count: number}>('SELECT COUNT(*) as count FROM sync_state');

    if (syncStateCount.count === 0) {
      console.log('[SyncAPI] sync_state table empty, rebuilding...');
      await this.syncStateUpdater.rebuildSyncState();
    }

    // Existing initialization logic...
  }
}
```

## Phase 6: Migration Strategy

### 6.1 Database Migration
**File**: `electron/main/database/database.ts`

Add migration in `handleDatabaseMigrations`:

```typescript
// Add sync_state table improvements
try {
  // Check if new columns exist
  const tableInfo = await allAsync(db, "PRAGMA table_info(sync_state)");
  const hasCompositeId = tableInfo.some(col => col.name === 'composite_id');

  if (!hasCompositeId) {
    console.log('Migrating sync_state table...');

    // Add new columns
    await runAsync(db, 'ALTER TABLE sync_state ADD COLUMN composite_id TEXT');
    await runAsync(db, 'ALTER TABLE sync_state ADD COLUMN item_name TEXT');
    await runAsync(db, 'ALTER TABLE sync_state ADD COLUMN sync_path TEXT');
    await runAsync(db, 'ALTER TABLE sync_state ADD COLUMN last_modified TEXT');

    // Populate composite_id for existing records
    await runAsync(db, `
      UPDATE sync_state
      SET composite_id = item_type || '_' || item_id
      WHERE composite_id IS NULL
    `);

    console.log('sync_state table migration completed');
  }
} catch (error) {
  console.error('Error migrating sync_state table:', error);
}
```

### 6.2 Gradual Rollout
1. **Phase 6.1**: Deploy schema changes
2. **Phase 6.2**: Enable sync_state population via hooks
3. **Phase 6.3**: Enable fast change detection (with fallback)
4. **Phase 6.4**: Remove legacy change detection after validation

## Phase 7: Testing & Validation

### 7.1 Performance Testing
Create benchmarks to measure improvement:

```typescript
// In tests/sync-performance.test.ts
describe('Sync Performance', () => {
  it('should be faster with sync_state table', async () => {
    // Create test data (1000 items)
    await createTestData(1000);

    // Measure legacy detection
    const legacyStart = Date.now();
    await changeDetector.compareStatesLegacy(manifest, syncPath);
    const legacyTime = Date.now() - legacyStart;

    // Measure fast detection
    const fastStart = Date.now();
    await fastChangeDetector.compareWithSyncState(manifest);
    const fastTime = Date.now() - fastStart;

    expect(fastTime).toBeLessThan(legacyTime * 0.1); // Should be 10x faster
  });
});
```

### 7.2 Data Integrity Testing
Ensure sync_state stays in sync with actual data:

```typescript
describe('Sync State Integrity', () => {
  it('should maintain sync_state accuracy', async () => {
    // Create/update/delete items
    const book = await createBook(testBookData);
    await updateBook(book.id, updatedData);
    await deleteBook(book.id);

    // Verify sync_state reflects changes
    const syncState = await dbAll('SELECT * FROM sync_state WHERE item_type = "book"');
    expect(syncState).toHaveLength(0); // Should be deleted
  });
});
```

## Expected Performance Improvements

### Before (Current System)
- **Startup sync check**: 2-5 seconds for 1000 items
- **Database queries**: 3 separate queries (books, folders, notes)
- **Memory usage**: High (loads all items into memory)

### After (With sync_state)
- **Startup sync check**: 100-200ms for 1000 items
- **Database queries**: 1 optimized query
- **Memory usage**: Low (only hash comparison)

## Implementation Timeline

- **Week 1**: Phase 1-2 (Schema updates, database hooks)
- **Week 2**: Phase 3-4 (Fast change detection, sync state maintenance)
- **Week 3**: Phase 5-6 (Performance optimizations, migration)
- **Week 4**: Phase 7 (Testing, validation, deployment)

## Risk Mitigation

1. **Fallback Strategy**: Keep legacy change detection as backup
2. **Data Validation**: Regular sync_state integrity checks
3. **Gradual Rollout**: Enable features incrementally
4. **Monitoring**: Track performance metrics and error rates

This plan provides a comprehensive approach to integrating the improved sync_state table while maintaining system reliability and achieving significant performance gains.
```
